#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手势文本输入GUI界面
创建一个正方形窗口，包含四个主要区域：
- 顶部：绘制微多音轨控制
- 左下：300单词显示区域
- 右上：历史输入结果显示
- 右下：当前输入结果显示
"""

import tkinter as tk
from tkinter import ttk, scrolledtext
import random

class GestureInputGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("手势文本输入系统")

        # 设置正方形窗口 800x800
        window_size = 800
        self.root.geometry(f"{window_size}x{window_size}")
        self.root.resizable(False, False)  # 固定窗口大小

        # 居中显示窗口
        self.center_window(window_size, window_size)

        # 创建主框架
        self.create_main_layout()

        # 初始化数据
        self.word_list = self.generate_sample_words()
        self.history_results = []
        self.current_input = ""
        self.uncertain_chars = []

        # 初始化界面内容
        self.update_word_display()
        self.update_current_input_display()

    def center_window(self, width, height):
        """将窗口居中显示"""
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.root.geometry(f"{width}x{height}+{x}+{y}")

    def create_main_layout(self):
        """创建主要布局"""
        # 创建主容器
        main_frame = tk.Frame(self.root, bg='white')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 顶部区域 - 绘制微多音轨控制 (高度约150px)
        self.create_top_section(main_frame)

        # 底部区域 - 分为左右两部分
        bottom_frame = tk.Frame(main_frame, bg='white')
        bottom_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))

        # 左下区域 - 300单词显示
        self.create_left_bottom_section(bottom_frame)

        # 右侧区域 - 分为上下两部分
        right_frame = tk.Frame(bottom_frame, bg='white')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 0))

        # 右上区域 - 历史输入结果
        self.create_right_top_section(right_frame)

        # 右下区域 - 当前输入结果
        self.create_right_bottom_section(right_frame)

    def create_top_section(self, parent):
        """创建顶部绘制微多音轨控制区域"""
        top_frame = tk.LabelFrame(parent, text="绘制微多音轨控制",
                                 font=('Microsoft YaHei', 12, 'bold'),
                                 bg='lightblue', height=150)
        top_frame.pack(fill=tk.X, pady=(0, 5))
        top_frame.pack_propagate(False)

        # 添加一些控制按钮
        control_frame = tk.Frame(top_frame, bg='lightblue')
        control_frame.pack(expand=True)

        tk.Button(control_frame, text="开始录制", font=('Microsoft YaHei', 10),
                 bg='#4CAF50', fg='white', width=10).pack(side=tk.LEFT, padx=5)
        tk.Button(control_frame, text="停止录制", font=('Microsoft YaHei', 10),
                 bg='#f44336', fg='white', width=10).pack(side=tk.LEFT, padx=5)
        tk.Button(control_frame, text="播放", font=('Microsoft YaHei', 10),
                 bg='#2196F3', fg='white', width=10).pack(side=tk.LEFT, padx=5)
        tk.Button(control_frame, text="清除", font=('Microsoft YaHei', 10),
                 bg='#FF9800', fg='white', width=10).pack(side=tk.LEFT, padx=5)

    def create_left_bottom_section(self, parent):
        """创建左下300单词显示区域"""
        left_frame = tk.LabelFrame(parent, text="300单词显示区域",
                                  font=('Microsoft YaHei', 10, 'bold'),
                                  bg='white')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 说明文字
        desc_label = tk.Label(left_frame, text="这里显示300单词，显示形式如下：",
                             font=('Microsoft YaHei', 9), bg='white')
        desc_label.pack(anchor=tk.W, padx=5, pady=5)

        # 单词按钮显示区域
        self.word_display_frame = tk.Frame(left_frame, bg='white')
        self.word_display_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建带滚动条的框架
        canvas = tk.Canvas(self.word_display_frame, bg='white')
        scrollbar = ttk.Scrollbar(self.word_display_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = tk.Frame(canvas, bg='white')

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_right_top_section(self, parent):
        """创建右上历史输入结果区域"""
        right_top_frame = tk.LabelFrame(parent, text="历史输入单词结果",
                                       font=('Microsoft YaHei', 10, 'bold'),
                                       bg='white')
        right_top_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))

        # 历史结果显示区域
        history_container = tk.Frame(right_top_frame, bg='white')
        history_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        self.history_text = scrolledtext.ScrolledText(history_container,
                                                     font=('Microsoft YaHei', 10),
                                                     wrap=tk.WORD, height=8)
        self.history_text.pack(fill=tk.BOTH, expand=True)

        # 清空按钮
        clear_btn = tk.Button(right_top_frame, text="清空",
                             font=('Microsoft YaHei', 9),
                             bg='#f44336', fg='white',
                             command=self.clear_history)
        clear_btn.pack(side=tk.RIGHT, padx=5, pady=5)

    def create_right_bottom_section(self, parent):
        """创建右下当前输入结果区域"""
        right_bottom_frame = tk.LabelFrame(parent, text="当前输入结果",
                                          font=('Microsoft YaHei', 10, 'bold'),
                                          bg='white')
        right_bottom_frame.pack(fill=tk.BOTH, expand=True)

        # 说明文字
        desc_label = tk.Label(right_bottom_frame,
                             text="这里显示当前正在输入的结果，\n当单词没有完整输入时，随机显示\n后面3个字符为灰色，表示一种\n不确定状态",
                             font=('Microsoft YaHei', 9), bg='white',
                             justify=tk.LEFT)
        desc_label.pack(anchor=tk.W, padx=5, pady=5)

        # 当前输入显示区域
        self.current_input_frame = tk.Frame(right_bottom_frame, bg='white')
        self.current_input_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 使用Text widget来支持不同颜色的文字
        self.current_input_text = tk.Text(self.current_input_frame,
                                         font=('Microsoft YaHei', 14, 'bold'),
                                         bg='white', fg='black',
                                         height=3, wrap=tk.WORD,
                                         relief=tk.FLAT, bd=0,
                                         state=tk.DISABLED)
        self.current_input_text.pack(fill=tk.BOTH, expand=True)

        # 配置文字标签
        self.current_input_text.tag_configure("certain", foreground="black")
        self.current_input_text.tag_configure("uncertain", foreground="gray")

    def generate_sample_words(self):
        """生成示例单词列表"""
        words = [
            "about", "what", "hello", "world", "python", "programming", "computer",
            "science", "technology", "artificial", "intelligence", "machine", "learning",
            "data", "analysis", "algorithm", "software", "development", "coding",
            "debugging", "testing", "deployment", "version", "control", "database"
        ]

        # 扩展到300个单词
        extended_words = []
        for i in range(300):
            extended_words.append(f"{words[i % len(words)]}{i//len(words) + 1}")

        return extended_words

    def update_word_display(self):
        """更新单词显示区域"""
        # 清除现有按钮
        for widget in self.scrollable_frame.winfo_children():
            widget.destroy()

        # 创建单词按钮
        sample_words = ["about", "what", "hello", "world", "python", "computer",
                       "science", "technology", "artificial", "intelligence"]

        row = 0
        col = 0
        max_cols = 4  # 每行最多4个按钮

        for i, word in enumerate(sample_words):
            if i < 2:  # 前两个单词使用粉色
                btn = tk.Button(self.scrollable_frame, text=word,
                               font=('Microsoft YaHei', 10),
                               bg='#FFB6C1', fg='#8B008B',
                               relief=tk.RAISED, bd=2,
                               command=lambda w=word: self.on_word_click(w))
            else:  # 其他单词使用默认颜色
                btn = tk.Button(self.scrollable_frame, text=word,
                               font=('Microsoft YaHei', 10),
                               bg='#E0E0E0', fg='black',
                               relief=tk.RAISED, bd=1,
                               command=lambda w=word: self.on_word_click(w))

            btn.grid(row=row, column=col, padx=2, pady=2, sticky='ew')

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        # 配置列权重使按钮均匀分布
        for i in range(max_cols):
            self.scrollable_frame.columnconfigure(i, weight=1)

    def update_current_input_display(self):
        """更新当前输入显示"""
        # 模拟当前输入：确定部分 + 不确定部分（灰色）
        certain_part = "hel"
        uncertain_part = "lo"

        # 清除现有内容
        self.current_input_text.config(state=tk.NORMAL)
        self.current_input_text.delete(1.0, tk.END)

        # 插入确定部分（黑色）
        self.current_input_text.insert(tk.END, certain_part, "certain")

        # 插入不确定部分（灰色）
        self.current_input_text.insert(tk.END, uncertain_part, "uncertain")

        self.current_input_text.config(state=tk.DISABLED)

    def on_word_click(self, word):
        """处理单词按钮点击事件"""
        self.add_to_history(word)

        # 模拟更新当前输入
        import random
        prefixes = ["", "h", "he", "hel", "hell"]
        suffixes = ["o", "lo", "llo", "ello"]

        prefix = random.choice(prefixes)
        suffix = random.choice(suffixes)

        # 更新当前输入显示
        self.current_input_text.config(state=tk.NORMAL)
        self.current_input_text.delete(1.0, tk.END)

        if prefix:
            self.current_input_text.insert(tk.END, prefix, "certain")
        if suffix:
            self.current_input_text.insert(tk.END, suffix, "uncertain")

        self.current_input_text.config(state=tk.DISABLED)

    def clear_history(self):
        """清空历史记录"""
        self.history_text.delete(1.0, tk.END)
        self.history_results.clear()

    def add_to_history(self, word):
        """添加单词到历史记录"""
        self.history_results.append(word)
        self.history_text.insert(tk.END, word + " ")
        self.history_text.see(tk.END)

    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    app = GestureInputGUI()
    app.run()

if __name__ == "__main__":
    main()
