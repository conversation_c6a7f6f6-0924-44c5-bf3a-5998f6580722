#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手势文本输入GUI界面 - PyQt5版本
创建一个现代化窗口，包含四个主要区域：
- 顶部：微多普勒特征绘制区域 (使用pyqtgraph)
- 左下：300单词显示区域 (带Fluent Design样式)
- 右上：历史输入结果显示
- 右下：当前输入结果显示
"""

import sys
import random
import numpy as np
from PyQt5.QtWidgets import *
from PyQt5.QtCore import *
from PyQt5.QtGui import *
import pyqtgraph as pg
from pyqtgraph import GraphicsLayoutWidget
import matplotlib.colors as mcolors
import difflib

# 自定义Parula颜色映射
def create_parula_colormap():
    """创建MATLAB parula颜色映射"""
    parula_colors = [
        [0.2081, 0.1663, 0.5292],
        [0.2116, 0.1898, 0.5777],
        [0.2123, 0.2138, 0.6270],
        [0.2081, 0.2386, 0.6771],
        [0.1959, 0.2645, 0.7279],
        [0.1707, 0.2919, 0.7792],
        [0.1253, 0.3242, 0.8303],
        [0.0591, 0.3598, 0.8683],
        [0.0117, 0.3875, 0.8820],
        [0.0060, 0.4086, 0.8828],
        [0.0165, 0.4266, 0.8786],
        [0.0329, 0.4430, 0.8720],
        [0.0498, 0.4586, 0.8641],
        [0.0629, 0.4737, 0.8554],
        [0.0723, 0.4887, 0.8467],
        [0.0779, 0.5040, 0.8384],
        [0.0793, 0.5200, 0.8312],
        [0.0749, 0.5375, 0.8263],
        [0.0641, 0.5570, 0.8240],
        [0.0488, 0.5772, 0.8228],
        [0.0343, 0.5966, 0.8199],
        [0.0265, 0.6137, 0.8135],
        [0.0239, 0.6287, 0.8038],
        [0.0231, 0.6418, 0.7913],
        [0.0228, 0.6535, 0.7768],
        [0.0267, 0.6642, 0.7607],
        [0.0384, 0.6743, 0.7436],
        [0.0590, 0.6838, 0.7254],
        [0.0843, 0.6928, 0.7062],
        [0.1133, 0.7015, 0.6859],
        [0.1453, 0.7098, 0.6646],
        [0.1801, 0.7177, 0.6424],
        [0.2178, 0.7250, 0.6193],
        [0.2586, 0.7317, 0.5954],
        [0.3022, 0.7376, 0.5712],
        [0.3482, 0.7424, 0.5473],
        [0.3953, 0.7459, 0.5244],
        [0.4420, 0.7481, 0.5033],
        [0.4871, 0.7491, 0.4840],
        [0.5300, 0.7491, 0.4661],
        [0.5709, 0.7485, 0.4494],
        [0.6099, 0.7473, 0.4337],
        [0.6473, 0.7456, 0.4188],
        [0.6834, 0.7435, 0.4044],
        [0.7184, 0.7411, 0.3905],
        [0.7525, 0.7384, 0.3768],
        [0.7858, 0.7356, 0.3633],
        [0.8185, 0.7327, 0.3498],
        [0.8507, 0.7299, 0.3360],
        [0.8824, 0.7274, 0.3217],
        [0.9139, 0.7258, 0.3063],
        [0.9450, 0.7261, 0.2886],
        [0.9739, 0.7314, 0.2666],
        [0.9938, 0.7455, 0.2403],
        [0.9990, 0.7653, 0.2164],
        [0.9955, 0.7861, 0.1967],
        [0.9880, 0.8066, 0.1794],
        [0.9789, 0.8271, 0.1633],
        [0.9697, 0.8481, 0.1475],
        [0.9626, 0.8705, 0.1309],
        [0.9589, 0.8949, 0.1132],
        [0.9598, 0.9218, 0.0948],
        [0.9661, 0.9514, 0.0755],
        [0.9763, 0.9831, 0.0538]
    ]
    return parula_colors

# 自定义Fluent Design切换开关类
class FluentToggleSwitch(QCheckBox):
    def __init__(self, text="", parent=None):
        super().__init__(text, parent)
        self.setFixedHeight(40)
        self.setup_style()

    def setup_style(self):
        """设置Fluent Design样式"""
        self.setStyleSheet("""
            QCheckBox {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                color: #323130;
                spacing: 10px;
                padding: 5px;
            }
            QCheckBox::indicator {
                width: 50px;
                height: 24px;
                border-radius: 12px;
                background-color: #E1E1E1;
                border: 2px solid #E1E1E1;
            }
            QCheckBox::indicator:checked {
                background-color: #0078D4;
                border: 2px solid #0078D4;
            }
            QCheckBox::indicator:hover {
                border: 2px solid #106EBE;
            }
            QCheckBox::indicator:checked:hover {
                background-color: #106EBE;
                border: 2px solid #106EBE;
            }
            QCheckBox::indicator::before {
                content: '';
                width: 18px;
                height: 18px;
                border-radius: 9px;
                background-color: white;
                position: absolute;
                left: 3px;
                top: 3px;
                transition: left 0.3s ease;
            }
            QCheckBox::indicator:checked::before {
                left: 29px;
            }
        """)

# 自定义单词按钮类
class WordButton(QPushButton):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.word_text = text
        self.delete_button = None
        self.setup_ui()

    def setup_ui(self):
        """设置按钮UI"""
        self.setMinimumHeight(40)
        self.setStyleSheet(self.get_normal_style())

    def get_normal_style(self):
        """获取正常状态样式"""
        return """
            QPushButton {
                background-color: #FFB6C1;
                border: 2px solid #FF69B4;
                border-radius: 8px;
                color: #8B008B;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                font-weight: bold;
                padding: 8px 16px;
            }
            QPushButton:hover {
                background-color: #FFC0CB;
                border: 2px solid #FF1493;
                box-shadow: 0 4px 8px rgba(255, 105, 180, 0.3);
            }
            QPushButton:pressed {
                background-color: #FFB6C1;
                border: 2px solid #DC143C;
            }
        """

    def enterEvent(self, event):
        """鼠标进入事件"""
        super().enterEvent(event)
        if not self.delete_button:
            self.create_delete_button()

    def leaveEvent(self, event):
        """鼠标离开事件"""
        super().leaveEvent(event)
        if self.delete_button:
            self.delete_button.hide()

    def create_delete_button(self):
        """创建删除按钮"""
        self.delete_button = QPushButton("×", self)
        self.delete_button.setFixedSize(20, 20)
        self.delete_button.move(self.width() - 25, 5)
        self.delete_button.setStyleSheet("""
            QPushButton {
                background-color: #FF4444;
                border: none;
                border-radius: 10px;
                color: white;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #FF0000;
            }
        """)
        self.delete_button.clicked.connect(self.delete_word)
        self.delete_button.show()

    def delete_word(self):
        """删除单词"""
        reply = QMessageBox.question(self, '确认删除',
                                   f'确定要删除单词 "{self.word_text}" 吗？',
                                   QMessageBox.Yes | QMessageBox.No)
        if reply == QMessageBox.Yes:
            # 找到父窗口（GestureInputGUI实例）
            parent_window = self.parent()
            while parent_window and not hasattr(parent_window, 'remove_word'):
                parent_window = parent_window.parent()

            if parent_window and hasattr(parent_window, 'remove_word'):
                parent_window.remove_word(self.word_text)
            else:
                print(f"无法找到父窗口来删除单词: {self.word_text}")

    def resizeEvent(self, event):
        """窗口大小改变事件"""
        super().resizeEvent(event)
        if self.delete_button:
            self.delete_button.move(self.width() - 25, 5)

class GestureInputGUI(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("手势文本输入系统 - PyQt5版本")
        self.setMinimumSize(900, 700)
        self.resize(1200, 900)

        # 初始化数据
        self.word_list = self.generate_sample_words()
        self.history_results = []
        self.current_input = ""

        # 切换开关状态
        self.vocabulary_constraint_enabled = False  # 限制词汇范围开关状态
        self.history_assistance_enabled = False     # 历史记录辅助开关状态

        # 设置应用样式
        self.setup_fluent_style()

        # 创建主界面
        self.setup_ui()

        # 居中显示窗口
        self.center_window()

    def setup_fluent_style(self):
        """设置Fluent Design样式"""
        style = """
            QMainWindow {
                background-color: #F3F3F3;
            }
            QGroupBox {
                font-family: 'Microsoft YaHei';
                font-size: 14px;
                font-weight: bold;
                color: #323130;
                border: 2px solid #E1E1E1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
                background-color: white;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                background-color: white;
            }
            QPushButton {
                background-color: #0078D4;
                border: none;
                border-radius: 4px;
                color: white;
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                padding: 8px 16px;
                min-height: 20px;
            }
            QPushButton:hover {
                background-color: #106EBE;
                box-shadow: 0 2px 4px rgba(0, 120, 212, 0.3);
            }
            QPushButton:pressed {
                background-color: #005A9E;
            }
            QTextEdit {
                border: 1px solid #E1E1E1;
                border-radius: 4px;
                background-color: white;
                font-family: 'Microsoft YaHei';
                font-size: 11px;
                padding: 8px;
            }
            QScrollArea {
                border: none;
                background-color: white;
            }
        """
        self.setStyleSheet(style)

    def center_window(self):
        """将窗口居中显示"""
        screen = QApplication.desktop().screenGeometry()
        size = self.geometry()
        self.move((screen.width() - size.width()) // 2,
                  (screen.height() - size.height()) // 2)

    def setup_ui(self):
        """设置主界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(10)
        main_layout.setContentsMargins(15, 15, 15, 15)

        # 创建顶部区域 - 微多普勒特征绘制
        self.create_top_section(main_layout)

        # 创建底部水平布局
        bottom_layout = QHBoxLayout()
        bottom_layout.setSpacing(10)

        # 创建左下区域 - 300单词显示
        self.create_left_bottom_section(bottom_layout)

        # 创建右侧垂直布局
        right_layout = QVBoxLayout()
        right_layout.setSpacing(10)

        # 创建右上区域 - 历史输入结果
        self.create_right_top_section(right_layout)

        # 创建右下区域 - 当前输入结果
        self.create_right_bottom_section(right_layout)

        # 添加右侧布局到底部布局
        right_widget = QWidget()
        right_widget.setLayout(right_layout)
        bottom_layout.addWidget(right_widget)

        # 添加底部布局到主布局
        bottom_widget = QWidget()
        bottom_widget.setLayout(bottom_layout)
        main_layout.addWidget(bottom_widget)

        # 初始化界面内容
        self.update_word_display()
        self.update_current_input_display()

        # 初始化单词区域状态
        self.update_word_area_state()

    def create_top_section(self, parent_layout):
        """创建顶部微多普勒特征绘制区域"""
        # 创建组框
        top_group = QGroupBox("微多普勒特征绘制区域")
        top_group.setMinimumHeight(200)

        # 创建布局
        top_layout = QVBoxLayout(top_group)

        # 创建pyqtgraph绘图区域
        self.plot_widget = GraphicsLayoutWidget()
        self.plot_widget.setBackground('w')

        # 创建图像项
        self.plot_item = self.plot_widget.addPlot(title="微多普勒特征")
        self.image_item = pg.ImageItem()
        self.plot_item.addItem(self.image_item)

        # 设置parula颜色映射（不添加颜色条）
        parula_colors = create_parula_colormap()
        colormap = pg.ColorMap(np.linspace(0, 1, len(parula_colors)), parula_colors)
        self.image_item.setColorMap(colormap)

        top_layout.addWidget(self.plot_widget)

        # 创建控制按钮区域（只保留开始录制按钮）
        control_layout = QHBoxLayout()

        # 创建开始录制按钮
        start_btn = QPushButton("开始录制")
        start_btn.setStyleSheet("background-color: #4CAF50;")
        start_btn.clicked.connect(self.start_recording)

        # 添加按钮到布局
        control_layout.addWidget(start_btn)
        control_layout.addStretch()

        top_layout.addLayout(control_layout)
        parent_layout.addWidget(top_group)

        # 初始化示例数据
        self.update_plot_data()

    def create_left_bottom_section(self, parent_layout):
        """创建左下300单词显示区域"""
        # 创建组框
        left_group = QGroupBox("300单词显示区域")
        left_group.setMinimumWidth(400)

        # 创建布局
        left_layout = QVBoxLayout(left_group)

        # 说明文字
        desc_label = QLabel("这里显示300单词，显示形式如下：")
        desc_label.setStyleSheet("font-size: 11px; color: #666666; margin: 5px;")
        left_layout.addWidget(desc_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建单词容器
        self.word_container = QWidget()
        self.word_layout = QGridLayout(self.word_container)
        self.word_layout.setSpacing(8)

        scroll_area.setWidget(self.word_container)
        left_layout.addWidget(scroll_area)

        parent_layout.addWidget(left_group)

    def create_right_top_section(self, parent_layout):
        """创建右上历史输入结果区域"""
        # 创建组框
        right_top_group = QGroupBox("历史输入单词结果")

        # 创建布局
        right_top_layout = QVBoxLayout(right_top_group)

        # 创建历史文本显示区域
        self.history_text = QTextEdit()
        self.history_text.setReadOnly(True)
        self.history_text.setMaximumHeight(150)
        self.history_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Microsoft YaHei';
                font-size: 12px;
                line-height: 1.4;
            }
        """)
        right_top_layout.addWidget(self.history_text)

        # 创建清空按钮
        clear_btn = QPushButton("清空")
        clear_btn.setStyleSheet("background-color: #f44336;")
        clear_btn.clicked.connect(self.clear_history)
        clear_btn.setMaximumWidth(80)

        # 创建按钮布局
        btn_layout = QHBoxLayout()
        btn_layout.addStretch()
        btn_layout.addWidget(clear_btn)

        right_top_layout.addLayout(btn_layout)
        parent_layout.addWidget(right_top_group)

    def create_right_bottom_section(self, parent_layout):
        """创建右下当前输入结果区域"""
        # 创建组框
        right_bottom_group = QGroupBox("当前输入结果")

        # 创建布局
        right_bottom_layout = QVBoxLayout(right_bottom_group)

        # 说明文字
        desc_label = QLabel("这里显示当前正在输入的结果，\n当单词没有完整输入时，随机显示\n后面3个字符为灰色，表示一种\n不确定状态")
        desc_label.setStyleSheet("font-size: 11px; color: #666666; margin: 5px;")
        desc_label.setWordWrap(True)
        right_bottom_layout.addWidget(desc_label)

        # 当前输入显示区域
        self.current_input_text = QTextEdit()
        self.current_input_text.setReadOnly(True)
        self.current_input_text.setMaximumHeight(100)
        self.current_input_text.setStyleSheet("""
            QTextEdit {
                font-family: 'Microsoft YaHei';
                font-size: 16px;
                font-weight: bold;
                border: none;
                background-color: transparent;
            }
        """)
        right_bottom_layout.addWidget(self.current_input_text)

        # 添加切换开关区域
        self.create_toggle_switches(right_bottom_layout)

        parent_layout.addWidget(right_bottom_group)

    def create_toggle_switches(self, parent_layout):
        """创建切换开关区域"""
        # 创建切换开关容器
        toggle_container = QWidget()
        toggle_layout = QHBoxLayout(toggle_container)
        toggle_layout.setContentsMargins(5, 10, 5, 5)
        toggle_layout.setSpacing(20)

        # 创建第一个切换开关 - 限制词汇范围
        self.vocabulary_constraint_toggle = FluentToggleSwitch("限制词汇范围")
        self.vocabulary_constraint_toggle.setChecked(self.vocabulary_constraint_enabled)
        self.vocabulary_constraint_toggle.stateChanged.connect(self.on_vocabulary_constraint_changed)
        toggle_layout.addWidget(self.vocabulary_constraint_toggle)

        # 创建第二个切换开关 - 历史记录辅助
        self.history_assistance_toggle = FluentToggleSwitch("历史记录辅助")
        self.history_assistance_toggle.setChecked(self.history_assistance_enabled)
        self.history_assistance_toggle.stateChanged.connect(self.on_history_assistance_changed)
        toggle_layout.addWidget(self.history_assistance_toggle)

        # 添加弹性空间
        toggle_layout.addStretch()

        parent_layout.addWidget(toggle_container)

    def generate_sample_words(self):
        """生成示例单词列表"""
        words = [
            "about", "what", "hello", "world", "python", "programming", "computer",
            "science", "technology", "artificial", "intelligence", "machine", "learning",
            "data", "analysis", "algorithm", "software", "development", "coding",
            "debugging", "testing", "deployment", "version", "control", "database"
        ]

        # 扩展到300个单词
        extended_words = []
        for i in range(300):
            extended_words.append(f"{words[i % len(words)]}{i//len(words) + 1}")

        return extended_words

    def update_word_display(self):
        """更新单词显示区域"""
        # 清除现有按钮
        for i in reversed(range(self.word_layout.count())):
            child = self.word_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        # 获取当前单词列表
        if hasattr(self, 'current_word_list'):
            words_to_display = self.current_word_list
        else:
            # 初始化单词列表
            self.current_word_list = ["about", "what", "hello", "world", "python", "computer",
                                     "science", "technology", "artificial", "intelligence"]
            words_to_display = self.current_word_list

        row = 0
        col = 0
        max_cols = 4  # 每行最多4个按钮

        for i, word in enumerate(words_to_display):
            # 创建自定义单词按钮
            btn = WordButton(word, self.word_container)
            # 移除自动添加到历史记录的功能 - 单词按钮现在只用于显示
            # btn.clicked.connect(lambda checked, w=word: self.on_word_click(w))

            self.word_layout.addWidget(btn, row, col)

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        # 设置列拉伸
        for i in range(max_cols):
            self.word_layout.setColumnStretch(i, 1)

        print(f"单词显示已更新，当前显示 {len(words_to_display)} 个单词")

    def update_current_input_display(self):
        """更新当前输入显示"""
        # 模拟当前输入：确定部分 + 不确定部分（灰色）
        certain_part = "hel"
        uncertain_part = "lo"

        # 更新当前输入状态
        self.current_input = certain_part + uncertain_part

        # 应用切换开关功能
        processed_input = self.current_input

        # 如果启用了词汇范围限制
        if self.vocabulary_constraint_enabled:
            vocabulary = self.get_current_vocabulary()
            processed_input = self.find_closest_word(processed_input, vocabulary)

        # 如果启用了历史记录辅助
        if self.history_assistance_enabled:
            processed_input = self.apply_history_correction(processed_input, self.history_results)

        # 如果输入被处理过，显示处理后的结果
        if processed_input != self.current_input:
            # 显示处理后的完整单词
            html_text = f'<span style="color: black; font-weight: bold;">{processed_input}</span>'
        else:
            # 显示原始的确定+不确定部分
            html_text = f'<span style="color: black; font-weight: bold;">{certain_part}</span><span style="color: gray; font-weight: normal;">{uncertain_part}</span>'

        self.current_input_text.setHtml(html_text)

    def on_word_click(self, word):
        """处理单词按钮点击事件"""
        self.add_to_history(word)

        # 模拟更新当前输入
        prefixes = ["", "h", "he", "hel", "hell"]
        suffixes = ["o", "lo", "llo", "ello"]

        prefix = random.choice(prefixes)
        suffix = random.choice(suffixes)

        # 更新当前输入显示
        html_text = f'<span style="color: black; font-weight: bold;">{prefix}</span><span style="color: gray; font-weight: normal;">{suffix}</span>'
        self.current_input_text.setHtml(html_text)

    def clear_history(self):
        """清空历史记录"""
        self.history_text.clear()
        self.history_results.clear()

    def add_to_history(self, word):
        """添加单词到历史记录"""
        self.history_results.append(word)
        self.history_text.append(word + " ")

    def remove_word(self, word):
        """从词汇表中删除单词"""
        print(f"尝试删除单词: {word}")

        # 确保current_word_list存在
        if not hasattr(self, 'current_word_list'):
            self.current_word_list = ["about", "what", "hello", "world", "python", "computer",
                                     "science", "technology", "artificial", "intelligence"]

        # 从当前单词列表中删除
        if word in self.current_word_list:
            self.current_word_list.remove(word)
            print(f"单词 '{word}' 已从列表中删除")

            # 刷新单词显示
            self.update_word_display()
            print("单词显示已刷新")
        else:
            print(f"单词 '{word}' 不在当前列表中")

        # 同时从主词汇表中删除（如果存在）
        if hasattr(self, 'word_list') and word in self.word_list:
            self.word_list.remove(word)

    # 绘图相关方法
    def update_plot_data(self):
        """更新绘图数据"""
        # 生成示例微多普勒数据
        data = np.random.rand(64, 128) * 100
        self.image_item.setImage(data)

    def start_recording(self):
        """开始录制"""
        print("开始录制微多普勒数据...")
        # 这里可以添加实际的录制逻辑
        # 可以在这里启动数据采集、更新绘图等

    # 切换开关事件处理方法
    def on_vocabulary_constraint_changed(self, state):
        """词汇范围限制开关状态改变"""
        self.vocabulary_constraint_enabled = state == Qt.Checked
        print(f"词汇范围限制: {'开启' if self.vocabulary_constraint_enabled else '关闭'}")

        # 更新300单词显示区域的状态
        self.update_word_area_state()

        # 如果开启了词汇限制，重新处理当前输入
        if self.vocabulary_constraint_enabled and hasattr(self, 'current_input'):
            self.apply_vocabulary_constraint()

    def on_history_assistance_changed(self, state):
        """历史记录辅助开关状态改变"""
        self.history_assistance_enabled = state == Qt.Checked
        print(f"历史记录辅助: {'开启' if self.history_assistance_enabled else '关闭'}")

        # 如果开启了历史辅助，重新处理当前输入
        if self.history_assistance_enabled and hasattr(self, 'current_input'):
            self.apply_history_assistance()

    def update_word_area_state(self):
        """更新300单词显示区域的状态（激活/非激活）"""
        if hasattr(self, 'word_container'):
            if self.vocabulary_constraint_enabled:
                # 激活状态 - 正常显示
                self.word_container.setStyleSheet("""
                    QWidget {
                        background-color: white;
                    }
                """)
                self.word_container.setEnabled(True)
                print("300单词区域已激活")
            else:
                # 非激活状态 - 变暗且不可交互
                self.word_container.setStyleSheet("""
                    QWidget {
                        background-color: #F5F5F5;
                        color: #A0A0A0;
                    }
                """)
                self.word_container.setEnabled(False)
                print("300单词区域已禁用")

    def apply_vocabulary_constraint(self):
        """应用词汇范围限制"""
        if hasattr(self, 'current_input') and self.current_input:
            # 获取当前词汇表
            vocabulary = self.get_current_vocabulary()
            # 找到最接近的单词
            closest_word = self.find_closest_word(self.current_input, vocabulary)
            print(f"词汇限制: '{self.current_input}' -> '{closest_word}'")
            # 这里可以更新显示

    def apply_history_assistance(self):
        """应用历史记录辅助"""
        if hasattr(self, 'current_input') and self.current_input:
            # 应用历史记录辅助修正
            corrected_input = self.apply_history_correction(self.current_input, self.history_results)
            print(f"历史辅助: '{self.current_input}' -> '{corrected_input}'")
            # 这里可以更新显示

    # API接口方法
    def find_closest_word(self, input_text: str, vocabulary_list: list) -> str:
        """
        找到词汇表中最接近的单词

        Args:
            input_text: 输入的文本
            vocabulary_list: 词汇表列表

        Returns:
            最接近的单词
        """
        if not input_text or not vocabulary_list:
            return input_text

        # 使用difflib计算相似度
        closest_matches = difflib.get_close_matches(
            input_text, vocabulary_list, n=1, cutoff=0.3
        )

        if closest_matches:
            return closest_matches[0]
        else:
            # 如果没有足够相似的匹配，返回原文本
            return input_text

    def apply_history_correction(self, current_input: str, history_list: list) -> str:
        """
        基于历史记录进行单词修正

        Args:
            current_input: 当前输入的文本
            history_list: 历史记录列表

        Returns:
            修正后的文本
        """
        if not current_input or not history_list:
            return current_input

        # 简单的历史记录辅助逻辑示例
        # 检查历史记录中是否有相似的单词
        for history_word in reversed(history_list[-10:]):  # 只检查最近10个
            if history_word.lower().startswith(current_input.lower()):
                print(f"历史记录匹配: {current_input} -> {history_word}")
                return history_word

        # 如果没有匹配，返回原文本
        return current_input

    def get_current_vocabulary(self) -> list:
        """获取当前词汇表"""
        if hasattr(self, 'current_word_list'):
            return self.current_word_list
        else:
            return ["about", "what", "hello", "world", "python", "computer",
                   "science", "technology", "artificial", "intelligence"]

    def show(self):
        """显示窗口"""
        super().show()

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("手势文本输入系统")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("Gesture Input Lab")

    # 创建主窗口
    window = GestureInputGUI()
    window.show()

    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
