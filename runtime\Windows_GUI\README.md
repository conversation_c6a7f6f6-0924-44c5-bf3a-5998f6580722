# 手势文本输入GUI系统 - PyQt5版本

## 文件说明
- `gesture_input_gui.py` - 主GUI程序文件（PyQt5版本）

## 🚀 新版本特性

### 1. 技术栈升级
- **GUI框架**: PyQt5 (替代tkinter)
- **绘图库**: pyqtgraph (高性能科学绘图)
- **设计风格**: Microsoft Fluent Design System
- **颜色映射**: 自定义MATLAB parula colormap

### 2. 现代化界面设计
- **可调整窗口**: 最小尺寸900x700，支持自由调整大小
- **响应式布局**: 窗口大小改变时自动适应
- **Fluent Design**: 现代化的视觉效果和交互体验
- **专业外观**: 企业级应用的视觉标准

## 🔧 最新更新 (v2.2)

### 新增智能切换开关功能
- ✅ **Fluent Design切换开关**: 两个现代化的切换开关
- ✅ **限制词汇范围开关**:
  - 控制识别范围是否限制在300单词区域
  - 动态激活/禁用单词区域的交互状态
  - 实现最接近匹配算法
- ✅ **历史记录辅助开关**:
  - 基于历史记录进行智能单词修正
  - 提供API接口供外部调用
- ✅ **API接口实现**:
  - `find_closest_word()`: 词汇匹配算法
  - `apply_history_correction()`: 历史记录修正算法

### 之前修复 (v2.1)
- ✅ **移除颜色条**: 简化pyqtgraph绘图区域，移除颜色条显示
- ✅ **简化控制按钮**: 只保留"开始录制"按钮，移除停止、播放、清除按钮
- ✅ **修复问题A**: 单词按钮点击不再自动添加到历史记录
- ✅ **修复问题B**: 删除功能现在正常工作

## 功能特性

### 1. 顶部区域 - 微多普勒特征绘制（简化版）
- **pyqtgraph绘图**: 高性能实时数据可视化
- **parula颜色映射**: MATLAB标准科学可视化配色（无颜色条）
- **交互式绘图**: 支持缩放、平移等操作
- **简化控制**: 仅保留"开始录制"按钮
- **清洁界面**: 移除了停止录制、播放、清除按钮以简化操作

### 2. 左下区域 - 300单词显示（修复版）
- **自定义单词按钮**: 带粉色边框的现代化按钮设计
- **hover效果**: 鼠标悬停时显示删除按钮
- **删除功能**: 点击X按钮可从词汇表中删除单词（已修复）
- **确认对话框**: 删除前弹出确认提示
- **纯显示功能**: 单词按钮不再自动添加到历史记录（已修复）
- **动态刷新**: 删除单词后界面立即更新
- **滚动支持**: 流畅的滚动浏览体验
- **网格布局**: 每行4个按钮，自动换行

### 3. 右上区域 - 历史输入结果
- **现代化文本框**: 支持富文本显示
- **清空功能**: 一键清除所有历史记录
- **自动滚动**: 新内容自动滚动到可见区域

### 4. 右下区域 - 当前输入结果（增强版）
- **HTML富文本**: 支持不同颜色和样式的文字
- **实时更新**: 黑色确定字符 + 灰色不确定字符
- **动态效果**: 模拟真实的输入过程
- **智能切换开关**: 两个Fluent Design风格的切换开关

#### 切换开关功能
- **限制词汇范围**:
  - 开启时：限制识别范围到300单词区域，激活单词区域交互
  - 关闭时：允许无限制识别，单词区域变暗且不可交互
- **历史记录辅助**:
  - 开启时：基于历史记录进行智能单词修正
  - 关闭时：禁用历史辅助功能

## 🎨 设计亮点

### Microsoft Fluent Design风格
- **现代化配色**: 专业的企业级配色方案
- **阴影效果**: 按钮hover时的微妙阴影
- **圆角设计**: 8px圆角带来现代感
- **一致性**: 统一的字体、间距和视觉层次

### 交互体验优化
- **流畅动画**: 按钮状态切换的平滑过渡
- **直观操作**: 鼠标悬停即显示操作选项
- **确认机制**: 重要操作前的用户确认
- **响应式**: 适应不同屏幕尺寸

## 运行方法

### 依赖安装
```bash
pip install PyQt5 pyqtgraph matplotlib numpy
```

### 启动程序
```bash
python gesture_input_gui.py
```

## 技术实现

### 核心技术栈
- **GUI框架**: PyQt5 5.15+
- **绘图库**: pyqtgraph 0.12+
- **数值计算**: NumPy
- **颜色处理**: matplotlib.colors
- **字体**: Microsoft YaHei (微软雅黑)

### 关键特性
- **自定义parula colormap**: 64色MATLAB标准科学可视化配色
- **WordButton类**: 支持hover效果和删除功能的自定义按钮
- **GraphicsLayoutWidget**: 高性能科学数据可视化
- **HTML富文本**: 支持多色文字显示
- **响应式布局**: QVBoxLayout + QHBoxLayout + QGridLayout

### 颜色方案
- **粉色按钮**: #FFB6C1 背景, #FF69B4 边框, #8B008B 文字
- **Fluent蓝**: #0078D4 主色调
- **确定文字**: 黑色粗体
- **不确定文字**: 灰色常规
- **背景色**: #F3F3F3 浅灰

## 🔧 扩展功能

### 已实现的高级功能
- **单词删除**: 右键删除不需要的词汇
- **实时绘图**: 微多普勒特征的实时可视化
- **数据录制**: 模拟数据采集和播放功能
- **响应式设计**: 适应不同屏幕尺寸

### 可扩展功能
- 连接实际的雷达数据采集系统
- 实现真实的手势识别算法
- 添加数据导出和导入功能
- 实现用户配置文件
- 添加多语言支持
- 集成机器学习模型

## 界面预览

程序启动后会显示一个现代化的可调整大小窗口，包含：

- **顶部**: 高性能pyqtgraph绘图区域，支持实时数据可视化
- **左下**: 带删除功能的智能单词按钮网格
- **右上**: 现代化的历史记录显示区域
- **右下**: 支持富文本的当前输入状态显示

所有区域都采用Microsoft Fluent Design风格，提供专业、现代的用户体验。
