# 手势文本输入GUI系统

## 文件说明
- `gesture_input_gui.py` - 主GUI程序文件

## 功能特性

### 1. 窗口设计
- **正方形窗口**: 800x800像素，固定大小，居中显示
- **四区域布局**: 按照您的设计图分为四个主要区域

### 2. 区域功能

#### 顶部区域 - 绘制微多音轨控制
- 包含录制控制按钮：开始录制、停止录制、播放、清除
- 高度约150像素，浅蓝色背景

#### 左下区域 - 300单词显示
- 显示可点击的单词按钮
- 前两个单词（about、what）使用粉色背景，突出显示
- 其他单词使用灰色背景
- 支持滚动显示更多单词
- 每行显示4个单词按钮

#### 右上区域 - 历史输入结果
- 显示已选择的单词历史记录
- 包含"清空"按钮，可清除所有历史记录
- 支持滚动查看历史内容

#### 右下区域 - 当前输入结果
- 显示当前正在输入的单词
- **特色功能**: 支持黑色确定字符 + 灰色不确定字符显示
- 模拟实时输入状态，展示输入的不确定性

### 3. 交互功能
- **单词点击**: 点击左下区域的单词按钮会将单词添加到历史记录
- **历史管理**: 可以查看和清空历史输入记录
- **实时显示**: 当前输入区域会动态更新显示状态

## 运行方法

```bash
python gesture_input_gui.py
```

## 技术实现
- **GUI框架**: tkinter (Python标准库)
- **字体**: Microsoft YaHei (微软雅黑)
- **颜色方案**: 
  - 粉色按钮: #FFB6C1 背景, #8B008B 文字
  - 灰色按钮: #E0E0E0 背景, black 文字
  - 确定文字: 黑色
  - 不确定文字: 灰色

## 扩展功能
程序设计为模块化结构，可以轻松扩展以下功能：
- 连接实际的手势识别系统
- 添加更多单词到词库
- 实现真实的音轨录制和播放
- 添加配置文件支持
- 实现数据持久化

## 界面预览
程序启动后会显示一个800x800的正方形窗口，包含：
- 顶部的控制按钮区域
- 左下的单词选择区域（带粉色高亮按钮）
- 右上的历史记录区域（带清空功能）
- 右下的当前输入显示区域（支持灰色不确定字符）

所有区域都有清晰的边框和标题，界面简洁易用。
